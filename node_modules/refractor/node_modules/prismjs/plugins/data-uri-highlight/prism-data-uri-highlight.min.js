!function(){if("undefined"!=typeof Prism){var e={pattern:/(.)\bdata:[^\/]+\/[^,]+,(?:(?!\1)[\s\S]|\\\1)+(?=\1)/,lookbehind:!0,inside:{"language-css":{pattern:/(data:[^\/]+\/(?:[^+,]+\+)?css,)[\s\S]+/,lookbehind:!0},"language-javascript":{pattern:/(data:[^\/]+\/(?:[^+,]+\+)?javascript,)[\s\S]+/,lookbehind:!0},"language-json":{pattern:/(data:[^\/]+\/(?:[^+,]+\+)?json,)[\s\S]+/,lookbehind:!0},"language-markup":{pattern:/(data:[^\/]+\/(?:[^+,]+\+)?(?:html|xml),)[\s\S]+/,lookbehind:!0}}},r=["url","attr-value","string"];Prism.plugins.dataURIHighlight={processGrammar:function(i){i&&!i["data-uri"]&&(Prism.languages.DFS(i,function(i,a,n){-1<r.indexOf(n)&&!Array.isArray(a)&&(a.pattern||(a=this[i]={pattern:a}),a.inside=a.inside||{},"attr-value"==n?Prism.languages.insertBefore("inside",a.inside["url-link"]?"url-link":"punctuation",{"data-uri":e},a):a.inside["url-link"]?Prism.languages.insertBefore("inside","url-link",{"data-uri":e},a):a.inside["data-uri"]=e)}),i["data-uri"]=e)}},Prism.hooks.add("before-highlight",function(i){if(e.pattern.test(i.code))for(var a in e.inside)if(e.inside.hasOwnProperty(a)&&!e.inside[a].inside&&e.inside[a].pattern.test(i.code)){var n=a.match(/^language-(.+)/)[1];Prism.languages[n]&&(e.inside[a].inside={rest:(r=Prism.languages[n],Prism.plugins.autolinker&&Prism.plugins.autolinker.processGrammar(r),r)})}var r;Prism.plugins.dataURIHighlight.processGrammar(i.grammar)})}}();