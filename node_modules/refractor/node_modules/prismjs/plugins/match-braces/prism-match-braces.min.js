!function(){if("undefined"!=typeof Prism&&"undefined"!=typeof document){var d={"(":")","[":"]","{":"}"},u={"(":"brace-round","[":"brace-square","{":"brace-curly"},f={"${":"{"},h=0,n=/^(pair-\d+-)(close|open)$/;Prism.hooks.add("complete",function(e){var t=e.element,n=t.parentElement;if(n&&"PRE"==n.tagName){var r=[];if(Prism.util.isActive(t,"match-braces")&&r.push("(","[","{"),0!=r.length){n.__listenerAdded||(n.addEventListener("mousedown",function(){var e=n.querySelector("code"),t=p("brace-selected");Array.prototype.slice.call(e.querySelectorAll("."+t)).forEach(function(e){e.classList.remove(t)})}),Object.defineProperty(n,"__listenerAdded",{value:!0}));var o=Array.prototype.slice.call(t.querySelectorAll("span."+p("token")+"."+p("punctuation"))),l=[];r.forEach(function(e){for(var t=d[e],n=p(u[e]),r=[],c=[],s=0;s<o.length;s++){var i=o[s];if(0==i.childElementCount){var a=i.textContent;(a=f[a]||a)===e?(l.push({index:s,open:!0,element:i}),i.classList.add(n),i.classList.add(p("brace-open")),c.push(s)):a===t&&(l.push({index:s,open:!1,element:i}),i.classList.add(n),i.classList.add(p("brace-close")),c.length&&r.push([s,c.pop()]))}}r.forEach(function(e){var t="pair-"+h+++"-",n=o[e[0]],r=o[e[1]];n.id=t+"open",r.id=t+"close",[n,r].forEach(function(e){e.addEventListener("mouseenter",v),e.addEventListener("mouseleave",m),e.addEventListener("click",b)})})});var c=0;l.sort(function(e,t){return e.index-t.index}),l.forEach(function(e){e.open?(e.element.classList.add(p("brace-level-"+(c%12+1))),c++):(c=Math.max(0,c-1),e.element.classList.add(p("brace-level-"+(c%12+1))))})}}})}function p(e){var t=Prism.plugins.customClass;return t?t.apply(e,"none"):e}function e(e){var t=n.exec(e.id);return document.querySelector("#"+t[1]+("open"==t[2]?"close":"open"))}function v(){Prism.util.isActive(this,"brace-hover",!0)&&[this,e(this)].forEach(function(e){e.classList.add(p("brace-hover"))})}function m(){[this,e(this)].forEach(function(e){e.classList.remove(p("brace-hover"))})}function b(){Prism.util.isActive(this,"brace-select",!0)&&[this,e(this)].forEach(function(e){e.classList.add(p("brace-selected"))})}}();