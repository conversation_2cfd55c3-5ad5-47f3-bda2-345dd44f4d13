/**
 * KaibanJS Tasks for Super Agent Workflow
 *
 * This file defines all the tasks that agents will execute in sequence
 * to complete the super agent workflow using KaibanJS framework.
 * 
 * Following the 7-phase super agent workflow pattern with enhanced
 * task definitions and proper agent assignments.
 */

import { Task } from 'kaibanjs';
import { superAgentTeam } from './agents';

// Console logging utility for Kaiban Tasks
const logKaibanTask = (message: string, data?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`📋 [KAIBAN-TASK] ${timestamp}: ${message}`);
  if (data) {
    console.log(`📝 [KAIBAN-TASK-DATA]:`, data);
  }
};

logKaibanTask('Initializing KaibanJS Super Agent Tasks');

/**
 * Phase 1: Topic Analysis Task (Qwen - High Reasoning + Tavily Web Research)
 * Comprehensive topic analysis with web research and strategic planning
 */
export const topicAnalysisTask = new Task({
  description: `<thinking>
    I need to conduct comprehensive topic analysis using both my reasoning capabilities and web research.
    Let me start by understanding the topic landscape through web research, then apply strategic analysis.

    My approach:
    1. First, conduct broad web searches to understand the current landscape
    2. Analyze search results to identify key themes and trends
    3. Perform targeted searches for specific aspects
    4. Synthesize findings into a comprehensive research strategy

    I should start wide with general searches, then narrow down to specific aspects.
    </thinking>

    **Your Role:** You are a Senior Research Strategist & Topic Intelligence Analyst with expertise in web research, trend analysis, and strategic research planning.

    **CRITICAL INSTRUCTIONS:**
    - Use the Tavily search tool extensively to understand the current topic landscape
    - If Tavily search encounters connection issues, proceed with analysis based on your knowledge
    - Start with broad searches, then progressively narrow focus
    - Analyze search results to identify patterns, trends, and gaps
    - Think like an expert human researcher conducting preliminary investigation
    - Handle search failures gracefully and continue with comprehensive analysis

    **Phase 1: Web Research & Discovery**
    1. **Initial Landscape Search**: Search for the main topic to understand current state
    2. **Trend Analysis**: Search for "topic + trends 2025" and "topic + latest developments"
    3. **Expert Perspectives**: Search for "topic + expert opinions" and "topic + industry leaders"
    4. **Competitive Analysis**: Search for "best content about topic" and "topic + comprehensive guide"
    5. **Problem Identification**: Search for "topic + challenges" and "topic + problems"
    6. **Solution Mapping**: Search for "topic + solutions" and "topic + best practices"

    **Phase 2: Strategic Analysis (After Web Research)**
    1. **Topic Decomposition**: Break down into 4-6 key subtopics based on research findings
    2. **Keyword Intelligence**: Extract high-value terms from search results
    3. **Content Gap Analysis**: Identify what's missing in existing content
    4. **Audience Intent Mapping**: Determine what users are actually searching for
    5. **Authority Source Identification**: Find the most credible sources in the space
    6. **Competitive Landscape**: Map key players and their content strategies
    7. **Trend Integration**: Identify 2025 trends to incorporate
    8. **Search Query Optimization**: Create targeted queries for subsequent research phases

    **Research Strategy Framework:**
    - Execute 6-8 strategic web searches using Tavily
    - Analyze each search result for insights and patterns
    - Document key findings and authoritative sources
    - Create a comprehensive research roadmap

    **Topic to Analyze:** {topic}
    **Content Type:** {contentType}
    **Target Audience:** {targetAudience}
    **Word Count Target:** {wordCount}

    **EXECUTION APPROACH:**
    1. Attempt broad topic searches to understand the landscape
    2. If search tools are unavailable, use your extensive knowledge base
    3. Progressively narrow searches based on initial findings
    4. Focus on recent content (2024-2025) for current trends
    5. Prioritize authoritative sources and expert perspectives
    6. Document all findings with source attribution
    7. Synthesize research into actionable strategy

    **FALLBACK STRATEGY (if search tools fail):**
    - Leverage your comprehensive knowledge base for topic analysis
    - Focus on established patterns and known industry trends
    - Provide strategic analysis based on fundamental principles
    - Include recommendations for manual research verification
    - Maintain high-quality analysis standards regardless of tool availability`,
  expectedOutput: `Comprehensive web-research-based topic analysis including:
    - Current topic landscape analysis with key findings from web research
    - Authoritative sources and expert perspectives discovered
    - Trending developments and 2025 insights
    - Strategic subtopic breakdown based on research findings
    - High-value keywords and semantic terms from actual search results
    - Content gaps identified through competitive analysis
    - Targeted search queries for subsequent research phases
    - Research strategy roadmap with priority areas
    - Source credibility assessment and authority mapping
    - Audience intent analysis based on search patterns`,
  agent: superAgentTeam[0], // TopicAnalyst (Qwen + Tavily)
});

/**
 * Phase 2: Content Strategy Task (Gemini)
 * Develop comprehensive content strategy based on topic analysis
 */
export const contentStrategyTask = new Task({
  description: `Develop a comprehensive content strategy based on the topic analysis.
    
    **Your Role:** You are a Professional Content Strategist specializing in audience-focused content planning.
    
    **Task Requirements:**
    1. **Content Goal Definition**: Define clear content objectives
    2. **Audience Targeting**: Refine target audience based on analysis
    3. **Content Structure**: Design optimal content structure and flow
    4. **Key Messages**: Identify 3-5 core messages to communicate
    5. **Hook Development**: Create compelling opening hooks
    6. **Call-to-Action Strategy**: Design effective CTAs
    7. **SEO Strategy**: Develop keyword strategy and search intent alignment
    8. **Narrative Structure**: Plan story arc and content flow
    9. **Engagement Elements**: Identify opportunities for audience engagement
    10. **Content Differentiation**: Define unique value proposition
    
    **Input:** Use the topic analysis results from the previous task.
    
    **Output Format:**
    Provide a detailed content strategy document with actionable recommendations.`,
  expectedOutput: `Detailed content strategy including:
    - Content goals and audience targeting
    - Recommended content structure and flow
    - Key messages and narrative structure
    - Hook strategies and call-to-action plans
    - SEO strategy with keyword recommendations
    - Engagement and differentiation strategies`,
  agent: superAgentTeam[1], // ContentStrategist (Gemini)
});

/**
 * Phase 3: Primary Research Task (Gemini + Tavily)
 * Conduct comprehensive primary research using advanced search strategies
 */
export const primaryResearchTask = new Task({
  description: `<thinking>
    I need to conduct systematic primary research using the strategic queries from topic analysis.
    I should follow expert research methodologies: start broad, evaluate sources, extract insights,
    and organize findings systematically. I'll use parallel search strategies to cover multiple
    aspects simultaneously while maintaining source quality standards.
    </thinking>

    **Your Role:** You are a Senior Research Analyst specializing in systematic information gathering, source evaluation, and evidence synthesis.

    **RESEARCH METHODOLOGY:**
    Follow professional research standards used by expert analysts:
    1. Execute strategic queries from topic analysis systematically
    2. Evaluate each source for authority, recency, and relevance
    3. Extract key insights and supporting evidence
    4. Cross-reference information across multiple sources
    5. Organize findings by subtopic and importance
    6. Document source quality and credibility

    **ADVANCED SEARCH STRATEGY:**
    1. **Strategic Query Execution**: Use the optimized search queries from topic analysis
    2. **Source Diversification**: Target different types of sources (academic, industry, news, expert)
    3. **Authority Prioritization**: Focus on recognized experts and authoritative publications
    4. **Recency Filtering**: Prioritize 2024-2025 content for current insights
    5. **Cross-Validation**: Verify key claims across multiple sources
    6. **Evidence Hierarchy**: Rank sources by credibility and relevance

    **INFORMATION EXTRACTION FRAMEWORK:**
    1. **Key Insights**: Extract main findings and conclusions
    2. **Supporting Evidence**: Gather statistics, data points, and examples
    3. **Expert Perspectives**: Capture authoritative opinions and analysis
    4. **Trend Identification**: Document emerging patterns and developments
    5. **Practical Applications**: Find real-world implementations and case studies
    6. **Challenges & Solutions**: Identify problems and proposed solutions

    **QUALITY STANDARDS:**
    - Prioritize primary sources over secondary sources
    - Verify statistical claims and data points
    - Assess source bias and perspective
    - Document publication dates and author credentials
    - Cross-reference controversial or disputed claims
    - Filter out SEO-optimized content farms

    **PARALLEL RESEARCH APPROACH:**
    Execute 3-5 searches simultaneously covering:
    - Core topic fundamentals and definitions
    - Current trends and recent developments
    - Expert opinions and industry analysis
    - Statistical data and research findings
    - Practical applications and case studies

    **Tool Usage Instructions:**
    Use Tavily search tool with strategic queries. For each search:
    1. Analyze all returned results for quality and relevance
    2. Extract key information from top-quality sources
    3. Document source details for citation purposes
    4. Note any information gaps for deep research phase

    **If search tools encounter issues:**
    - Continue with comprehensive analysis using your knowledge base
    - Focus on established research methodologies and known sources
    - Provide detailed analysis based on fundamental principles
    - Include recommendations for manual verification

    **Input:** Use strategic search queries and research priorities from topic analysis and content strategy.`,
  expectedOutput: `Systematic research findings organized as:
    - Executive summary of key findings and insights
    - Subtopic-organized information with source attribution
    - Authority source analysis with credibility ratings
    - Current trends and 2025 developments with evidence
    - Statistical data and supporting evidence compilation
    - Expert perspectives and authoritative opinions
    - Practical applications and real-world examples
    - Information gaps identified for targeted deep research
    - Source quality assessment and reliability ratings
    - Cross-referenced claims and validated information`,
  agent: superAgentTeam[2], // PrimaryResearcher (Gemini + Tavily)
});

logKaibanTask('Primary research task created with Tavily integration');

/**
 * Phase 4: Gap Analysis Task (Qwen - High Reasoning)
 * Analyze research gaps and determine additional research needs
 */
export const gapAnalysisTask = new Task({
  description: `<thinking>
    I need to systematically analyze the primary research results to identify gaps and weaknesses.
    I should approach this like a quality assurance expert, comparing what we have against what we need
    for comprehensive content. I'll evaluate coverage, source diversity, depth, and identify specific
    areas that need additional research.
    </thinking>

    **Your Role:** You are a Research Quality Analyst and Information Architecture Expert with expertise in identifying information gaps and ensuring comprehensive coverage.

    **SYSTEMATIC GAP ANALYSIS METHODOLOGY:**
    Follow professional research quality assessment standards:
    1. Map research findings against content strategy requirements
    2. Identify coverage gaps in each subtopic area
    3. Assess source diversity and authority levels
    4. Evaluate information depth and detail sufficiency
    5. Check for bias patterns and perspective limitations
    6. Analyze currency and relevance of information

    **COMPREHENSIVE EVALUATION FRAMEWORK:**
    1. **Coverage Assessment**:
       - Compare research against all subtopics from topic analysis
       - Identify areas with insufficient information
       - Map content strategy requirements against available data

    2. **Source Quality Analysis**:
       - Evaluate source authority and credibility distribution
       - Assess need for more authoritative sources
       - Identify missing expert perspectives

    3. **Information Depth Evaluation**:
       - Determine areas needing deeper investigation
       - Identify superficial coverage requiring enhancement
       - Assess practical application and implementation details

    4. **Bias and Perspective Analysis**:
       - Identify potential biases in current research
       - Assess perspective diversity and balance
       - Determine need for alternative viewpoints

    5. **Currency and Relevance Check**:
       - Evaluate information recency and current relevance
       - Identify areas needing 2025 updates and trends
       - Assess need for recent developments and changes

    **PRIORITY RANKING SYSTEM:**
    Rank identified gaps by:
    - Critical importance to content objectives
    - Impact on content quality and authority
    - Audience value and interest level
    - Availability of reliable sources
    - Time and effort required for research

    **TARGETED RESEARCH RECOMMENDATIONS:**
    For each identified gap, provide:
    - Specific search queries to fill the gap
    - Recommended source types and authorities
    - Expected information depth and detail level
    - Priority level and research effort estimation

    **Input:** Analyze primary research results against topic analysis and content strategy requirements.`,
  expectedOutput: `Systematic gap analysis report including:
    - Executive summary of research quality and completeness
    - Detailed gap identification with priority rankings
    - Source diversity and authority assessment
    - Information depth evaluation by subtopic
    - Bias and perspective analysis with recommendations
    - Currency and relevance assessment
    - Specific targeted research recommendations with search queries
    - Priority-ranked action items for deep research phase
    - Quality improvement roadmap and success metrics`,
  agent: superAgentTeam[3], // GapAnalyst (Qwen)
});

logKaibanTask('Gap analysis task created for research quality assessment');

/**
 * Phase 5: Deep Research Task (Gemini + Tavily)
 * Conduct targeted deep research based on gap analysis
 */
export const deepResearchTask = new Task({
  description: `Conduct targeted deep research to fill identified gaps and enhance content comprehensiveness.
    
    **Your Role:** You are a Specialized Research Expert who excels at finding specific, detailed information to fill knowledge gaps.
    
    **Task Requirements:**
    1. **Targeted Searches**: Execute specific queries to fill identified gaps
    2. **Expert Source Finding**: Locate authoritative expert perspectives
    3. **Detailed Investigation**: Dive deep into complex aspects
    4. **Specialized Information**: Find technical or specialized details
    5. **Case Studies**: Locate relevant examples and case studies
    6. **Recent Developments**: Find latest news and developments
    7. **Statistical Enhancement**: Gather additional data and statistics
    8. **Comparative Analysis**: Find comparative information and benchmarks
    9. **Implementation Details**: Research practical applications and methods
    10. **Validation**: Cross-reference and validate critical information
    
    **Search Strategy:**
    - Focus on gap analysis recommendations
    - Use specialized and technical search terms
    - Target expert and authoritative sources
    - Search for recent developments and updates
    - Look for case studies and practical examples
    
    **Tool Usage:**
    Use the Tavily search tool with targeted queries based on gap analysis.
    
    **Input:** Use gap analysis recommendations and specific research needs identified.`,
  expectedOutput: `Targeted deep research results including:
    - Specific information filling identified gaps
    - Expert perspectives and authoritative sources
    - Detailed technical or specialized information
    - Case studies and practical examples
    - Recent developments and updates
    - Enhanced statistical data and evidence`,
  agent: superAgentTeam[4], // DeepResearcher (Gemini + Tavily)
});

logKaibanTask('Deep research task created for targeted investigation');

/**
 * Phase 6: Content Generation Task (Gemini + Tavily)
 * Generate superior content using keyword extraction and top 5 page analysis
 */
export const contentGenerationTask = new Task({
  description: `<thinking>
    I need to create superior content using a strategic approach:
    1. First, extract the single most important keyword from the topic using my analytical capabilities
    2. Use that keyword to search for the top 5 ranking pages
    3. Extract and analyze content from those pages
    4. Use the comprehensive research data to create superior content
    5. Ensure the content outranks existing articles through better information and structure

    This keyword-focused approach will help me find the most relevant competitive content and create something that surpasses it.
    </thinking>

    **Your Role:** You are an Elite Content Creator and SEO Expert with exceptional analytical skills, keyword intelligence, and expertise in creating content that outranks competitors through strategic research and superior writing.

    **CRITICAL MISSION:** Create the most informational, well-written, and engaging article that will outrank any existing content on this topic using strategic keyword extraction and competitive analysis.

    **PHASE 1: KEYWORD EXTRACTION & STRATEGIC SEARCH**
    1. **Single Keyword Extraction**:
       - Analyze the topic: "{topic}"
       - Extract the single most important, searchable keyword that represents the core of this topic
       - This should be the primary keyword that people would search for when looking for this information
       - Focus on the main concept, not modifiers or secondary terms
       - Example: For "Best AI Tools for Content Creation in 2025" → extract "AI tools"

    2. **Top 5 Page Research**:
       - Use the extracted keyword to search and find the top 5 ranking pages
       - Search query should be exactly the extracted keyword (simple, direct search)
       - Focus on pages that rank highest for this keyword
       - Prioritize comprehensive, authoritative content

    3. **Content Extraction & Analysis**:
       - Extract key content, structure, and insights from each of the top 5 pages
       - Analyze their writing patterns, information depth, and approach
       - Identify their strengths and weaknesses
       - Note their content structure and organization methods
       - Document their key points and supporting evidence

    **PHASE 2: COMPETITIVE ANALYSIS**
    1. **Content Gap Identification**: Find what's missing in the top 5 pages
    2. **Quality Assessment**: Evaluate the depth and accuracy of existing content
    3. **Structure Analysis**: Understand how top pages organize information
    4. **Engagement Evaluation**: Assess how well they engage readers
    5. **Authority Assessment**: Determine their credibility and source quality

    **PHASE 3: SUPERIOR CONTENT CREATION**
    1. **Enhanced Information**: Include comprehensive data from all previous research phases
    2. **Improved Structure**: Create better organization than top 5 pages
    3. **Advanced Engagement**: Implement superior engagement techniques
    4. **Comprehensive Coverage**: Fill all gaps found in competitor analysis
    5. **Modern Enhancement**: Add 2025 trends and cutting-edge insights
    6. **Practical Value**: Provide more actionable and valuable content

    **PHASE 4: ADVANCED CONTENT CREATION**
    1. **Keyword-Optimized Structure**: Organize content around the extracted keyword naturally
    2. **Comprehensive Information Integration**: Combine insights from:
       - All previous research phases (topic analysis, primary research, deep research)
       - Top 5 page content analysis
       - Gap analysis findings
       - Content strategy recommendations
    3. **Superior Writing Implementation**:
       - Create hooks that surpass competitor introductions
       - Use transition techniques better than top articles
       - Establish stronger credibility than competitors
       - Implement psychological triggers for reader retention
       - Pack more useful information per paragraph
       - Optimize for better reading experience
       - Create stronger emotional connections
       - Provide more actionable insights

    **CONTENT OPTIMIZATION STRATEGY:**
    - Use the extracted keyword naturally throughout the content
    - Exceed top 5 pages in information depth and quality
    - Include data and insights they missed
    - Provide better examples and explanations
    - Create superior structure and organization
    - Develop more compelling conclusions and CTAs

    **QUALITY STANDARDS:**
    - Must be more informative than the top 5 ranking pages
    - Must be more engaging than current top content
    - Must provide more practical value than competitors
    - Must have superior readability and flow
    - Must establish stronger authority through better sources
    - Must include more current and relevant information (2025 trends)

    **Content Guidelines:**
    - Follow the target word count: {wordCount}
    - Use the specified tone: {tone}
    - Address the target audience: {targetAudience}
    - Include 2025 trends and current year information
    - Reference authoritative sources from all research phases
    - Create paragraph-sized data points for better context
    - Strictly adhere to the exact topic without deviation
    - Naturally incorporate the extracted keyword for SEO optimization

    **EXECUTION WORKFLOW:**
    1. **Step 1**: Extract the single most important keyword from the topic
    2. **Step 2**: Search for top 5 pages using that exact keyword
    3. **Step 3**: Extract and analyze content from those 5 pages
    4. **Step 4**: Combine competitor insights with comprehensive research data
    5. **Step 5**: Create superior content that outranks all existing articles

    **Tool Usage:**
    Use Tavily search strategically:
    1. Search using the extracted keyword to find top 5 ranking pages
    2. Extract comprehensive content from each page
    3. Analyze their approaches and identify improvement opportunities

    **If search tools encounter issues:**
    - Use your knowledge of the topic and keyword optimization
    - Apply proven content creation techniques
    - Focus on creating comprehensive, engaging content using research data
    - Include recommendations for manual competitor verification

    **Input:** Use all previous task results including research findings, content strategy, gap analysis, and deep research, plus the extracted keyword and top 5 page analysis.`,
  expectedOutput: `Superior content created through keyword-focused competitive analysis including:
    - Extracted primary keyword from the topic with justification
    - Analysis of top 5 ranking pages for the extracted keyword
    - Content extraction and competitive insights from each page
    - Comprehensive comparison of competitor strengths and weaknesses
    - Enhanced content structure that surpasses top 5 pages
    - Complete article that outranks all existing content for the keyword
    - Superior information density using all research phases
    - Better organization and flow than top competitors
    - Stronger authority building through comprehensive source integration
    - More practical value and actionable insights than competitors
    - Strategic keyword optimization that feels natural and effective
    - Content that meets all specified requirements while dominating the keyword landscape`,
  agent: superAgentTeam[5], // ContentWriter (Gemini + Tavily)
});

logKaibanTask('Enhanced content generation task created with keyword extraction and top 5 page analysis');

/**
 * Export all tasks in execution order
 */
export const superAgentTasks = [
  topicAnalysisTask,
  contentStrategyTask,
  primaryResearchTask,
  gapAnalysisTask,
  deepResearchTask,
  contentGenerationTask,
];

/**
 * Task configuration for easy reference
 */
export const taskConfig = {
  tasks: superAgentTasks,
  taskNames: [
    'Topic Analysis',
    'Content Strategy',
    'Primary Research',
    'Gap Analysis',
    'Deep Research',
    'Content Generation'
  ],
  totalTasks: superAgentTasks.length,
  phaseDistribution: {
    qwen: ['Topic Analysis', 'Gap Analysis'],
    gemini: ['Content Strategy', 'Primary Research', 'Deep Research', 'Content Generation']
  },
  tasksWithTools: ['Topic Analysis', 'Primary Research', 'Deep Research', 'Content Generation']
};

logKaibanTask('KaibanJS Task System initialization complete', {
  status: 'ready',
  totalTasks: 6,
  sequentialExecution: true,
  taskDependencies: {
    'Content Strategy': ['Topic Analysis'],
    'Primary Research': ['Topic Analysis', 'Content Strategy'],
    'Gap Analysis': ['Topic Analysis', 'Content Strategy', 'Primary Research'],
    'Deep Research': ['Topic Analysis', 'Content Strategy', 'Primary Research', 'Gap Analysis'],
    'Content Generation': ['All Previous Tasks']
  },
  timestamp: new Date().toISOString()
});
